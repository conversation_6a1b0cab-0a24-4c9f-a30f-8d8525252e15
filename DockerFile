FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY pyrequirements.txt .
RUN pip install --no-cache-dir -r pyrequirements.txt

# Copy project
COPY ./app /app

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Default command
CMD ["python", "main.py"]
