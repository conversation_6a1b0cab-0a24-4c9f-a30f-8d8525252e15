import csv
import os

from crud import SECDBManager


def get_db_manager() -> SECDBManager:
    """Get database manager with connection string from environment or default."""
    db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
    return SECDBManager(db_url)

def load_exchanges(csv_file_path: str | None = None) -> bool:
    """
    Load exchanges from CSV file into database.

    Args:
        csv_file_path: Path to CSV file. If None, tries default path then creates sample data.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with get_db_manager() as db:
            if csv_file_path and os.path.exists(csv_file_path):
                with open(csv_file_path, newline='', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        # Check if exchange already exists
                        existing = db.get_exchange(row['exchange_code'])
                        if not existing:
                            success = db.add_exchange(
                                row['exchange_code'],
                                row['exchange_name'],
                                row.get('country', 'US')
                            )
                return True
            else:
                # If no file path is provided or file does not exist, return False
                return False
    except Exception as e:
        return False

if __name__ == "__main__":
    print(load_exchanges('mnt/a/data/usa_exchanges.csv'))
