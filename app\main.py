import csv
import os

from crud import SECDBManager


def get_db_manager() -> SECDBManager:
    """Get database manager with connection string from environment or default."""
    db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
    return SECDBManager(db_url)

def load_exchanges(csv_file_path: str | None = None) -> bool:
    """
    Load exchanges from CSV file into database.

    Args:
        csv_file_path: Path to CSV file. If None, tries default path then creates sample data.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"Attempting to load exchanges from: {csv_file_path}")

        # Check if file exists
        if csv_file_path and os.path.exists(csv_file_path):
            print(f"File found: {csv_file_path}")
        else:
            print(f"File not found: {csv_file_path}")
            print("Available files in /data:")
            if os.path.exists('/data'):
                for file in os.listdir('/data'):
                    print(f"  - {file}")
            else:
                print("  /data directory does not exist")
            return False

        with get_db_manager() as db:
            print("Database connection established")

            with open(csv_file_path, newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                count = 0
                for row in reader:
                    # Check if exchange already exists
                    existing = db.get_exchange(row['exchange_code'])
                    if not existing:
                        success = db.add_exchange(
                            row['exchange_code'],
                            row['exchange_name'],
                            row.get('country', 'US')
                        )
                        if success:
                            count += 1
                            print(f"Added exchange: {row['exchange_code']} - {row['exchange_name']}")
                        else:
                            print(f"Failed to add exchange: {row['exchange_code']}")
                    else:
                        print(f"Exchange already exists: {row['exchange_code']}")

                print(f"Successfully processed {count} new exchanges")
                return True

    except Exception as e:
        print(f"Error loading exchanges: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Try different possible paths for the CSV file
    possible_paths = [
        '/data/usa_exchanges.csv',
        'mnt/a/data/usa_exchanges.csv',
        '/mnt/a/data/usa_exchanges.csv',
        'usa_exchanges.csv'
    ]

    success = False
    for path in possible_paths:
        print(f"\nTrying path: {path}")
        if os.path.exists(path):
            print(f"Found file at: {path}")
            success = load_exchanges(path)
            break
        else:
            print(f"File not found at: {path}")

    if not success:
        print("\nNo CSV file found. Creating sample data...")
        # Create sample data if no file is found
        try:
            with get_db_manager() as db:
                sample_exchanges = [
                    ('NYSE', 'New York Stock Exchange', 'US'),
                    ('NASDAQ', 'NASDAQ Stock Market', 'US'),
                    ('LSE', 'London Stock Exchange', 'GB'),
                ]

                for code, name, country in sample_exchanges:
                    existing = db.get_exchange(code)
                    if not existing:
                        success = db.add_exchange(code, name, country)
                        if success:
                            print(f"Added sample exchange: {code} - {name}")
                        else:
                            print(f"Failed to add sample exchange: {code}")
                    else:
                        print(f"Sample exchange already exists: {code}")

                print("Sample data creation completed")
        except Exception as e:
            print(f"Error creating sample data: {e}")
            import traceback
            traceback.print_exc()
