if __name__ == "__main__":
    import argparse
    from db_helper import load_exchanges, load_sic_codes

    parser = argparse.ArgumentParser()
    parser.add_argument("--load", choices=["exchanges", "sic", "all"], help="Load data from CSV")
    args = parser.parse_args()
    if args.load == "exchanges":
        load_exchanges("/mnt/a/data/usa_exchanges.csv")
    elif args.load == "sic":
        load_sic_codes()
    elif args.load == "all":
        load_exchanges()
        load_sic_codes()
    else:
        print("Nothing to do")
