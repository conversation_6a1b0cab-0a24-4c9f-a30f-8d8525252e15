from crud import SECDBManager
import os

db_url = os.getenv('DATABASE_URL', '***********************************************/myapp')
with SECDBManager(db_url) as db:
    exchanges = db.get_all_exchanges()
    print(f'Total exchanges in database: {len(exchanges)}')
    for exchange in exchanges[:10]:
        print(f'  {exchange["exchange_code"]} - {exchange["exchange_name"]} ({exchange["country"]})')
    if len(exchanges) > 10:
        print(f'  ... and {len(exchanges) - 10} more')
