#!/bin/bash

# Script to run the Python application from WSL/Linux subsystem
# This script handles the proper path mapping for WSL

echo "🚀 Starting Docker containers from WSL..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Check if data directory exists
if [ ! -d "/mnt/a/data" ]; then
    echo "❌ Data directory /mnt/a/data not found."
    echo "   Make sure your a:/data directory exists on Windows."
    exit 1
fi

# Check if usa_exchanges.csv exists
if [ ! -f "/mnt/a/data/usa_exchanges.csv" ]; then
    echo "❌ File /mnt/a/data/usa_exchanges.csv not found."
    echo "   Available files in /mnt/a/data:"
    ls -la /mnt/a/data/ 2>/dev/null || echo "   Directory not accessible"
    exit 1
fi

echo "✅ Data file found: /mnt/a/data/usa_exchanges.csv"

# Build and run the application
echo "🔨 Building Docker containers..."
docker-compose build

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "🏃 Running the application..."
docker-compose up python-app

echo "✅ Application finished"
