#!/bin/bash

# Simple test script to verify data access from WSL
echo "🔍 Testing data access from WSL..."

echo "📁 Checking /mnt/a/data directory:"
if [ -d "/mnt/a/data" ]; then
    echo "✅ Directory exists"
    echo "📄 Files in directory:"
    ls -la /mnt/a/data/
else
    echo "❌ Directory /mnt/a/data does not exist"
    exit 1
fi

echo ""
echo "🎯 Checking usa_exchanges.csv:"
if [ -f "/mnt/a/data/usa_exchanges.csv" ]; then
    echo "✅ File exists"
    echo "📊 File info:"
    wc -l /mnt/a/data/usa_exchanges.csv
    echo "📝 First few lines:"
    head -3 /mnt/a/data/usa_exchanges.csv
else
    echo "❌ File /mnt/a/data/usa_exchanges.csv does not exist"
    exit 1
fi

echo ""
echo "🐳 Testing Docker volume mount:"
docker run --rm -v /mnt/a/data:/data alpine:latest ls -la /data/

echo ""
echo "✅ All tests passed! Data should be accessible from Docker containers."
